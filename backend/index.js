import express, { json } from 'express';
import { connect as mongoConnect, disconnect as mongoDisconnect } from 'mongoose';
import { config } from 'dotenv';
import cors from 'cors';
import schoolRoutes from './routes/schoolRoutes.js';
import authRoutes from './routes/authRoutes.js';
import testRoutes from './routes/testRoutes.js';
import questionRoutes from './routes/questionRoutes.js';
import curriculumRoutes from './routes/curriculumRoutes.js';
import studentCurriculumRoutes from './routes/studentCurriculumRoutes.js';
import modelDetails from './routes/modelDetailsRoutes.js';
import taskRoutes from './routes/taskRoutes.js';
import refreshRoutes from './routes/RefreshTokenRoutes.js';
import knowledgeGraphFetchDataRoute from './routes/knowledgeGraphFetchDataRoute.js';
import logoutRoute from './routes/UserLogoutRoutes.js'
import imageUploadRoutes from './routes/imageUploadRoutes.js';
import addQuestionRoute from './routes/AddQuestionsRoutes.js'
import testResultsRoutes from './routes/testResultsRoutes.js';
import classRoutes from './routes/classRoutes.js';
import recommendRoutes from './routes/RecommendQuestionsRoute.js';
import forgotPasswordRoutes from './routes/forgotPasswordRoutes.js';
import aegisGraderRoutes from './routes/aegisGraderRoutes.js';
import teachingAssistantRoutes from './routes/teachingAssistantRoutes.js';
import mergeAIQuestions from './routes/mergeAIQRoute.js';
import adminRoutes from './routes/adminRoutes.js';
import creditRoutes from './routes/creditRoutes.js';
import feedRoutes from './routes/FeedbackRoutes.js';
import tsListRoute from './routes/getSubjectTSListRoutes.js';
import chatRoutes from './routes/chatRoutes.js';
import analyticsRoutes from './routes/analyticsRoutes.js';
import proficiencyRoutes from './routes/proficiencyRoutes.js';
import mcpInitializationService from './services/mcpInitializationService.js';
import upscValidatorRoutes from './routes/upscValidator.js';
import s3Routes from './routes/s3Routes.js';
import securityInit from './utils/securityInit.js';

import cookieParser from 'cookie-parser';
// import serverAdapter from './bullBoard.js'; // Import BullBoard adapter

import { verifyJWT } from './middleware/verifyJWT.js';


import helmet from 'helmet';
import { rateLimiterMiddleware } from './middleware/rateLimiter.js';



config();

const app = express();
const PORT = process.env.PORT || 8080;
const MONGO_URI = process.env.NODE_ENV === 'development' ? process.env.MONGO_URI_TEST : process.env.MONGO_URI_PROD;

// CORS configuration for both development and production
if (process.env.NODE_ENV === 'development') {
    app.use(
        cors({
            origin: ['http://localhost:3000', 'http://localhost:8080'],
            methods: 'GET,POST,PUT,DELETE',
            credentials: true,
        })
    );
}

app.use(helmet());
app.use(json({ limit: '50mb' }));
app.use(cookieParser());

// app.use(rateLimiterMiddleware);

// Routes
app.use('/api/auth', authRoutes);
app.use('/api/refresh', refreshRoutes);
app.use('/api/add', addQuestionRoute);
app.use('/api/mergeAIQ', mergeAIQuestions);
app.use('/api/admin', adminRoutes);
app.use('/api/tslist', tsListRoute);
app.use('/api/feedback', feedRoutes);
app.use('/api/analytics', analyticsRoutes);
app.use('/api/proficiency', proficiencyRoutes);
app.use('/api/password', forgotPasswordRoutes)
app.use('/api/classes', classRoutes);
app.use('/api/student/curriculum', studentCurriculumRoutes);
// for now need to change back to authorization
app.use('/api/recommend', recommendRoutes);
app.use('/api/upscValidate', upscValidatorRoutes);

// all subsequent routes must be accessible only to the authenticated user
// Add BullBoard UI route before other routes
// app.use('/admin/queues', serverAdapter.getRouter());  //runs on 8080/admin/queues
// console.error('BullBoard UI available at /admin/queues');

app.use(verifyJWT);
app.use('api/classes', classRoutes);

app.use('/api/test', testRoutes);
app.use('/api/password', forgotPasswordRoutes)
app.use('/api/testResults', testResultsRoutes);
app.use('/api/schools', schoolRoutes);
app.use('/api/image', imageUploadRoutes);
app.use('/api/curriculum', curriculumRoutes);
app.use('/api/knowledgegraph', knowledgeGraphFetchDataRoute);
app.use('/api/aegisGrader', aegisGraderRoutes);
app.use('/api/teaching-assistant', teachingAssistantRoutes);
app.use('/api/chat', chatRoutes);
app.use('/api/questionbank', questionRoutes);
app.use('/api/details', modelDetails);
app.use('/api/tasks', taskRoutes);
app.use('/api/credits', creditRoutes);
app.use('/api/s3', s3Routes);

// Security health check endpoint
app.get('/api/security/health', async (req, res) => {
    try {
        const healthStatus = await securityInit.healthCheck();
        res.json({
            success: true,
            timestamp: new Date().toISOString(),
            security: healthStatus
        });
    } catch (error) {
        res.status(500).json({
            success: false,
            error: 'Health check failed',
            details: process.env.NODE_ENV === 'development' ? error.message : undefined
        });
    }
});

app.use('/api/logout', logoutRoute);

// Connect to MongoDB and start the server
const startServer = async () => {
    try {
        await mongoConnect(MONGO_URI);
        console.error('Connected to MongoDB');

        // Initialize MongoDB MCP service for enhanced AI capabilities
        console.error('Initializing MongoDB MCP service...');
        await mcpInitializationService.initializeWithRetry();

        // Initialize security systems
        console.error('Initializing security systems...');
        const securityStatus = await securityInit.initialize();
        if (!securityStatus.success) {
            console.error('⚠️ Security initialization failed:', securityStatus.error);
            console.error('⚠️ Server starting with reduced security features');
        }

        app.listen(PORT, () => {
            console.error(`Server running on port ${PORT}`);
            console.error('MongoDB MCP integration status:', mcpInitializationService.getStatus());
            console.error('Security systems status:', securityStatus.success ? '✅ Operational' : '⚠️ Degraded');
        });
    } catch (err) {
        console.error('Server startup error:', err);
        process.exit(1);
    }
};

process.on('SIGTERM', async () => {
    try {
        // Shutdown MCP services
        await mcpInitializationService.shutdown();

        // Close MongoDB connection
        await mongoDisconnect();
        console.error('MongoDB connection closed');

        process.exit(0);
    } catch (error) {
        console.error('Error during shutdown:', error.message);
        process.exit(1);
    }
});

// Start the server
startServer();

// Error handling middleware
app.use((err, req, res, next) => {
    console.error('Application error:', err.stack);
    res.status(500).json({
        error: 'Something broke!',
        details: process.env.NODE_ENV === 'development' ? err.message : undefined,
    });
});
