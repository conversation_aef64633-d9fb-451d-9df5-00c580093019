import express from 'express';
import { 
    generatePresignedUrl, 
    generatePdfViewUrl, 
    generateBatchPresignedUrls 
} from '../controllers/s3Controller.js';
import { authenticateToken } from '../middleware/authMiddleware.js';

const router = express.Router();

// Apply authentication middleware to all routes
router.use(authenticateToken);

/**
 * POST /api/s3/presigned-url
 * Generate a presigned URL for S3 operations
 * Body: { s3Key: string, operation?: 'getObject'|'putObject', expiresIn?: number }
 */
router.post('/presigned-url', generatePresignedUrl);

/**
 * POST /api/s3/pdf-view-url
 * Generate a presigned URL specifically for PDF viewing
 * Body: { s3Key: string }
 */
router.post('/pdf-view-url', generatePdfViewUrl);

/**
 * POST /api/s3/batch-presigned-urls
 * Generate presigned URLs for multiple files
 * Body: { s3Keys: string[], operation?: 'getObject'|'putObject', expiresIn?: number }
 */
router.post('/batch-presigned-urls', generateBatchPresignedUrls);

export default router;
