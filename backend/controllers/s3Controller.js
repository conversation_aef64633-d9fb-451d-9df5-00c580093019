import { S3 } from '@aws-sdk/client-s3';
import { getSignedUrl } from '@aws-sdk/s3-request-presigner';
import { GetObjectCommand, PutObjectCommand } from '@aws-sdk/client-s3';

// Configure AWS S3
const s3 = new S3({
    accessKeyId: process.env.AWS_ACCESS_KEY_ID,
    secretAccessKey: process.env.AWS_SECRET_ACCESS_KEY,
    region: process.env.AWS_REGION || 'ap-south-1',
});

/**
 * Generate presigned URL for S3 operations
 */
export const generatePresignedUrl = async (req, res) => {
    try {
        const { s3Key, operation = 'getObject', expiresIn = 3600 } = req.body;

        if (!s3Key) {
            return res.status(400).json({
                success: false,
                message: 'S3 key is required'
            });
        }

        const bucketName = process.env.UPLOAD_BUCKET_NAME || process.env.S3_BUCKET_NAME;
        
        if (!bucketName) {
            return res.status(500).json({
                success: false,
                message: 'S3 bucket configuration missing'
            });
        }

        let command;
        
        switch (operation) {
            case 'getObject':
                command = new GetObjectCommand({
                    Bucket: bucketName,
                    Key: s3Key,
                });
                break;
            case 'putObject':
                command = new PutObjectCommand({
                    Bucket: bucketName,
                    Key: s3Key,
                    ContentType: req.body.contentType || 'application/pdf',
                });
                break;
            default:
                return res.status(400).json({
                    success: false,
                    message: 'Invalid operation. Supported: getObject, putObject'
                });
        }

        const presignedUrl = await getSignedUrl(s3, command, {
            expiresIn: parseInt(expiresIn)
        });

        res.json({
            success: true,
            url: presignedUrl,
            expiresIn: expiresIn,
            operation: operation
        });

    } catch (error) {
        console.error('Error generating presigned URL:', error);
        res.status(500).json({
            success: false,
            message: 'Failed to generate presigned URL',
            error: error.message
        });
    }
};

/**
 * Generate presigned URL specifically for PDF viewing
 */
export const generatePdfViewUrl = async (req, res) => {
    try {
        const { s3Key } = req.body;

        if (!s3Key) {
            return res.status(400).json({
                success: false,
                message: 'S3 key is required'
            });
        }

        const bucketName = process.env.UPLOAD_BUCKET_NAME || process.env.S3_BUCKET_NAME;
        
        const command = new GetObjectCommand({
            Bucket: bucketName,
            Key: s3Key,
            ResponseContentType: 'application/pdf',
            ResponseContentDisposition: 'inline' // Display in browser instead of download
        });

        const presignedUrl = await getSignedUrl(s3, command, {
            expiresIn: 3600 // 1 hour
        });

        res.json({
            success: true,
            url: presignedUrl,
            expiresIn: 3600
        });

    } catch (error) {
        console.error('Error generating PDF view URL:', error);
        res.status(500).json({
            success: false,
            message: 'Failed to generate PDF view URL',
            error: error.message
        });
    }
};

/**
 * Batch generate presigned URLs for multiple files
 */
export const generateBatchPresignedUrls = async (req, res) => {
    try {
        const { s3Keys, operation = 'getObject', expiresIn = 3600 } = req.body;

        if (!Array.isArray(s3Keys) || s3Keys.length === 0) {
            return res.status(400).json({
                success: false,
                message: 'Array of S3 keys is required'
            });
        }

        const bucketName = process.env.UPLOAD_BUCKET_NAME || process.env.S3_BUCKET_NAME;
        
        const urls = await Promise.all(
            s3Keys.map(async (s3Key) => {
                try {
                    let command;
                    
                    if (operation === 'getObject') {
                        command = new GetObjectCommand({
                            Bucket: bucketName,
                            Key: s3Key,
                        });
                    } else {
                        command = new PutObjectCommand({
                            Bucket: bucketName,
                            Key: s3Key,
                        });
                    }

                    const presignedUrl = await getSignedUrl(s3, command, {
                        expiresIn: parseInt(expiresIn)
                    });

                    return {
                        s3Key,
                        url: presignedUrl,
                        success: true
                    };
                } catch (error) {
                    return {
                        s3Key,
                        error: error.message,
                        success: false
                    };
                }
            })
        );

        res.json({
            success: true,
            urls: urls,
            expiresIn: expiresIn
        });

    } catch (error) {
        console.error('Error generating batch presigned URLs:', error);
        res.status(500).json({
            success: false,
            message: 'Failed to generate batch presigned URLs',
            error: error.message
        });
    }
};
