import React, { useEffect, useMemo, useState } from 'react';
import { useLocation, useNavigate, useParams } from 'react-router-dom';
import { GradingStatus } from '../types/aegisGrader';
import { createPortal } from 'react-dom';
import { toast, ToastContainer } from 'react-toastify';
import {
    ArrowLeftIcon,
    ClockIcon,
    UsersIcon,
    ArrowTrendingUpIcon,
    MagnifyingGlassIcon,
    XMarkIcon,
} from '@heroicons/react/24/outline';
import {
    CheckCircleIcon as CheckCircleSolid,
} from '@heroicons/react/24/solid';
import QuestionBreakdownDisplay, { parseQuestionBreakdown } from '@/components/QuestionBreakdown';
import { createPdfViewerUrl } from '@/utils/s3Utils';

// --- Type Definitions ---
interface ParsedQuestion {
    number: string;
    marks_awarded: string;
    marks_possible: string;
    feedback: string;
}

interface ParsedSection {
    name: string;
    section_marks: string;
    section_possible_marks: string;
    question: ParsedQuestion[];
}

interface ParsedEvaluation {
    total_marks: string;
    maximum_possible_marks: string;
    percentage_score: string;
    section: ParsedSection[];
}

interface EvaluationParser {
    parseEvaluation: (evaluationResult: any) => ParsedEvaluation | null;
}

// --- Configuration for Different Data Formats ---
const EVALUATION_CONFIG: Record<string, EvaluationParser> = {
    // Current format: Array with markdown/XML string
    CURRENT: {
        parseEvaluation: (evaluationResult: any): ParsedEvaluation | null => {
            if (!evaluationResult || !Array.isArray(evaluationResult) || evaluationResult.length === 0) {
                return null;
            }

            const markdownContent = evaluationResult[0];
            if (typeof markdownContent !== 'string') return null;

            // Extract XML content from markdown
            const xmlMatch = markdownContent.match(/<evaluation>([\s\S]*?)<\/evaluation>/);
            if (!xmlMatch) return null;

            const xmlContent = `<evaluation>${xmlMatch[1]}</evaluation>`;
            return parseXMLEvaluation(xmlContent);
        }
    },
    // Future format: Direct object (for easy migration)
    FUTURE_OBJECT: {
        parseEvaluation: (evaluationResult: any): ParsedEvaluation | null => {
            if (!evaluationResult?.evaluation) return null;
            return evaluationResult.evaluation;
        }
    }
};

// XML Parser (easily replaceable)
const parseXMLEvaluation = (xmlString: string): ParsedEvaluation | null => {
    try {
        const parser = new DOMParser();
        const xmlDoc = parser.parseFromString(xmlString, 'text/xml');

        if (xmlDoc.getElementsByTagName("parsererror").length > 0) {
            console.error('XML parsing error');
            return null;
        }

        const evaluation = xmlDoc.querySelector('evaluation');
        if (!evaluation) return null;

        const totalMarks = evaluation.querySelector('total_marks')?.textContent || '0';
        const maxMarks = evaluation.querySelector('maximum_possible_marks')?.textContent || '0';
        const percentage = evaluation.querySelector('percentage_score')?.textContent || '0';

        const sections = Array.from(evaluation.querySelectorAll('section')).map((section): ParsedSection => {
            const questions = Array.from(section.querySelectorAll('question')).map((q): ParsedQuestion => ({
                number: q.querySelector('number')?.textContent || '0',
                marks_awarded: q.querySelector('marks_awarded')?.textContent || '0',
                marks_possible: q.querySelector('marks_possible')?.textContent || '0',
                feedback: q.querySelector('feedback')?.textContent || ''
            }));

            return {
                name: section.querySelector('name')?.textContent || '',
                section_marks: section.querySelector('section_marks')?.textContent || '0',
                section_possible_marks: section.querySelector('section_possible_marks')?.textContent || '0',
                question: questions
            };
        });

        return {
            total_marks: totalMarks,
            maximum_possible_marks: maxMarks,
            percentage_score: percentage,
            section: sections
        };
    } catch (error) {
        console.error('Error parsing XML evaluation:', error);
        return null;
    }
};

// --- Constants ---
const CONSTANTS = {
    TOAST_DELAY: 2000,
    DOWNLOAD_DELAY: 1000,
    // Change this to switch between formats easily
    CURRENT_FORMAT: 'CURRENT' as keyof typeof EVALUATION_CONFIG
};

// --- Type Definitions (Simplified) ---
interface GradingResult {
    questionNumber: number;
    maxMarks: number;
    marksAwarded: number;
    feedback: string;
}

interface AnswerSheetResult {
    id: string;
    studentName: string;
    rollNumber: string;
    totalMarks: number;
    maxMarks: number;
    percentage: number;
    results: GradingResult[];
    detailedBreakdown: any;
    pdfUrl?: string;
}

interface AnswerSheetData {
    id: string;
    studentName: string;
    rollNumber: string;
    pdfUrl?: string;
    evaluationResult?: any;
}

interface SubmissionData {
    id: string;
    status: GradingStatus;
    testDetails: {
        subject: string;
        className: string;
        date: string;
    };
    questionPaper?: { pdfUrl?: string };
    rubric?: { pdfUrl?: string };
    gradingProgress?: number;
    answerSheets: AnswerSheetData[];
}

// --- Helper Functions (Using Theme Classes) ---
const formatScore = (score: number | string | undefined): string => {
    const num = typeof score === 'string' ? parseFloat(score) : score;
    if (num === undefined || isNaN(num)) return '-';
    return num % 1 === 0 ? num.toString() : num.toFixed(1);
};

const getScoreColorClass = (percentage: number | undefined): string => {
    if (percentage === undefined || isNaN(percentage)) return "text-gray-500";
    if (percentage >= 80) return "text-success-600 dark:text-success-400";
    if (percentage >= 60) return "text-primary-600 dark:text-primary-400";
    if (percentage >= 40) return "text-warning-600 dark:text-warning-400";
    return "text-danger-600 dark:text-danger-400";
};

const getScoreBgClass = (percentage: number | undefined): string => {
    if (percentage === undefined || isNaN(percentage)) return "bg-gray-100 dark:bg-gray-800";
    if (percentage >= 80) return "bg-success-100 dark:bg-success-900/20";
    if (percentage >= 60) return "bg-primary-100 dark:bg-primary-900/20";
    if (percentage >= 40) return "bg-warning-100 dark:bg-warning-900/20";
    return "bg-danger-100 dark:bg-danger-900/20";
};

// --- Modal Component ---
const Modal: React.FC<{ isOpen: boolean; onClose: () => void; children: React.ReactNode }> = ({ isOpen, onClose, children }) => {
    useEffect(() => {
        const handleEsc = (e: KeyboardEvent) => {
            if (e.key === 'Escape') onClose();
        };
        if (isOpen) {
            document.addEventListener('keydown', handleEsc);
            document.body.style.overflow = 'hidden';
        }
        return () => {
            document.removeEventListener('keydown', handleEsc);
            document.body.style.overflow = 'unset';
        };
    }, [isOpen, onClose]);

    if (!isOpen) return null;

    return createPortal(
        <div className="fixed inset-0 bg-black/50 dark:bg-black/70 flex items-center justify-center z-50 p-4" onClick={onClose}>
            <div className="bg-background dark:bg-card rounded-lg shadow-xl w-full max-w-7xl max-h-[95vh] overflow-hidden" onClick={e => e.stopPropagation()}>
                {children}
            </div>
        </div>,
        document.body
    );
};

// --- Status Badge ---
const StatusBadge: React.FC<{ status: GradingStatus }> = ({ status }) => {
    const isCompleted = status === GradingStatus.COMPLETED;
    const classes = isCompleted
        ? 'bg-success-100 text-success-800 dark:bg-success-900/20 dark:text-success-400'
        : 'bg-primary-100 text-primary-800 dark:bg-primary-900/20 dark:text-primary-400';

    return (
        <div className={`px-3 py-1 rounded-full text-sm font-medium ${classes}`}>
            {isCompleted ? <CheckCircleSolid className="h-4 w-4 inline mr-1" /> : <ClockIcon className="h-4 w-4 inline mr-1" />}
            {status}
        </div>
    );
};

// --- Student Card ---
const StudentCard: React.FC<{
    sheet: AnswerSheetData;
    onViewResults: (result: AnswerSheetResult) => void;
    formatResults: (evaluationResult: any) => AnswerSheetResult | null;
}> = ({ sheet, onViewResults, formatResults }) => {
    const parsedEvaluation = useMemo(() => {
        return EVALUATION_CONFIG[CONSTANTS.CURRENT_FORMAT].parseEvaluation(sheet.evaluationResult);
    }, [sheet.evaluationResult]);

    const percentage = parsedEvaluation ? parseFloat(parsedEvaluation.percentage_score) : undefined;
    const totalMarks = parsedEvaluation ? parseFloat(parsedEvaluation.total_marks) : undefined;
    const maxMarks = parsedEvaluation ? parseFloat(parsedEvaluation.maximum_possible_marks) : undefined;
    console.log('Parsed evaluation:', parsedEvaluation);
    return (
        <div className="flex items-center justify-between p-4 border border-border dark:border-border rounded-lg hover:shadow-sm dark:hover:shadow-lg hover:border-primary/20 dark:hover:border-primary/30 transition-all duration-200">
            <div className="flex items-center gap-3 flex-1">
                <div className={`w-10 h-10 rounded-full ${getScoreBgClass(percentage)} flex items-center justify-center font-medium text-sm`}>
                    {sheet.studentName?.split(' ').map(n => n[0]).join('').toUpperCase() || '?'}
                </div>
                <div>
                    <p className="font-medium text-foreground">{sheet.studentName || "N/A"}</p>
                    <p className="text-sm text-muted-foreground">Roll: {sheet.rollNumber || "N/A"}</p>
                </div>
            </div>

            <div className="flex items-center gap-3">
                {parsedEvaluation ? (
                    <div className="text-right">
                        <p className={`font-semibold ${getScoreColorClass(percentage)}`}>
                            {formatScore(totalMarks)}/{formatScore(maxMarks)}
                        </p>
                        <p className={`text-sm ${getScoreColorClass(percentage)}`}>
                            {formatScore(percentage)}%
                        </p>
                    </div>
                ) : (
                    <div className="text-right">
                        <p className="text-muted-foreground">Not Graded</p>
                    </div>
                )}

                {parsedEvaluation && (
                    <button
                        className="px-3 py-2 bg-primary text-primary-foreground rounded-md hover:bg-primary/90 transition-colors"
                        onClick={() => {
                            const result = formatResults(sheet.evaluationResult);
                            if (result) onViewResults(result);
                        }}
                    >
                        View Details
                    </button>
                )}
            </div>
        </div>
    );
};

// --- Main Component ---
export const GradingDetails: React.FC = () => {
    const location = useLocation();
    const navigate = useNavigate();
    const { id: submissionId } = useParams<{ id: string }>();

    const [selectedResult, setSelectedResult] = useState<AnswerSheetResult | null>(null);
    const [searchTerm, setSearchTerm] = useState('');

    const submission = useMemo(() => {
        const history = location.state?.testHistory as SubmissionData[] | undefined;
        return history?.find((sub: SubmissionData) => sub.id === submissionId);
    }, [location.state?.testHistory, submissionId]);

    // --- Usage in your existing component ---
    const enhancedFormatResults = useMemo(() => (evaluationResult: any): AnswerSheetResult | null => {
        const parsedEvaluation = EVALUATION_CONFIG[CONSTANTS.CURRENT_FORMAT].parseEvaluation(evaluationResult);
        if (!parsedEvaluation) return null;

        // Parse the detailed breakdown
        const detailedBreakdown = parseQuestionBreakdown(evaluationResult);

        const sections = Array.isArray(parsedEvaluation.section) ? parsedEvaluation.section : [parsedEvaluation.section];
        const questions: GradingResult[] = sections.flatMap((section: ParsedSection) => {
            const questionsInSection = Array.isArray(section.question) ? section.question : [section.question];
            return questionsInSection.map((q: ParsedQuestion) => ({
                questionNumber: parseFloat(q.number) || 0,
                maxMarks: parseFloat(q.marks_possible) || 0,
                marksAwarded: parseFloat(q.marks_awarded) || 0,
                feedback: q.feedback || ''
            }));
        });

        questions.sort((a, b) => a.questionNumber - b.questionNumber);

        const sheet = submission?.answerSheets.find(s => s.evaluationResult === evaluationResult);

        return {
            id: sheet?.id || submissionId || '',
            studentName: sheet?.studentName || 'Unknown Student',
            rollNumber: sheet?.rollNumber || 'N/A',
            totalMarks: parseFloat(parsedEvaluation.total_marks) || 0,
            maxMarks: parseFloat(parsedEvaluation.maximum_possible_marks) || 0,
            percentage: parseFloat(parsedEvaluation.percentage_score) || 0,
            results: questions,
            detailedBreakdown,
            pdfUrl: sheet?.pdfUrl
        };
    }, [submission?.answerSheets, submissionId]);

    const filteredSheets = useMemo(() => {
        if (!submission?.answerSheets) return [];
        return submission.answerSheets.filter(sheet =>
            (sheet.studentName?.toLowerCase() || '').includes(searchTerm.toLowerCase()) ||
            (sheet.rollNumber?.toLowerCase() || '').includes(searchTerm.toLowerCase())
        );
    }, [submission?.answerSheets, searchTerm]);

    const classStats = useMemo(() => {
        if (!submission) return null;

        const gradedSheets = submission.answerSheets?.filter(sheet => {
            const parsed = EVALUATION_CONFIG[CONSTANTS.CURRENT_FORMAT].parseEvaluation(sheet.evaluationResult);
            return parsed !== null;
        });

        if (!gradedSheets || gradedSheets.length === 0) return null;

        const scores = gradedSheets.map(sheet => {
            const parsed = EVALUATION_CONFIG[CONSTANTS.CURRENT_FORMAT].parseEvaluation(sheet.evaluationResult);
            return parseFloat(parsed?.percentage_score || '0');
        });

        const average = scores.reduce((sum, score) => sum + score, 0) / scores.length;
        const highest = Math.max(...scores);
        const lowest = Math.min(...scores);

        return {
            average,
            highest,
            lowest,
            totalStudents: gradedSheets.length,
            totalSubmissions: submission.answerSheets.length
        };
    }, [submission]);

    if (!submission) {
        return (
            <div className="flex items-center justify-center min-h-screen bg-background">
                <div className="text-center">
                    <p className="text-muted-foreground mb-4">Submission not found</p>
                    <button onClick={() => navigate(-1)} className="text-primary hover:underline">
                        Go Back
                    </button>
                </div>
            </div>
        );
    }

    return (
        <div className="min-h-screen bg-background p-4">
            <ToastContainer position="top-right" autoClose={CONSTANTS.TOAST_DELAY} />

            <div className="max-w-6xl mx-auto space-y-6">
                {/* Header */}
                <div className="flex items-center justify-between">
                    <button
                        onClick={() => navigate(-1)}
                        className="flex items-center gap-2 text-muted-foreground hover:text-foreground transition-colors"
                    >
                        <ArrowLeftIcon className="h-5 w-5" />
                        Back
                    </button>
                    <StatusBadge status={submission.status} />
                </div>

                {/* Test Info */}
                <div className="bg-card dark:bg-card rounded-lg shadow-sm border border-border p-6">
                    <h1 className="text-2xl font-bold mb-2 text-foreground">
                        {submission.testDetails.subject || 'Unnamed Test'}
                    </h1>
                    <p className="text-muted-foreground">
                        {submission.testDetails.className} • {submission.testDetails.date}
                    </p>
                </div>

                <div className="grid grid-cols-1 lg:grid-cols-4 gap-6">
                    {/* Student List */}
                    <div className="lg:col-span-3">
                        <div className="bg-card dark:bg-card rounded-lg shadow-sm border border-border overflow-hidden">
                            <div className="p-4 border-b border-border">
                                <div className="flex items-center justify-between gap-4">
                                    <h2 className="text-lg font-semibold flex items-center gap-2 text-foreground">
                                        <UsersIcon className="h-5 w-5" />
                                        Students ({filteredSheets.length})
                                    </h2>
                                    <div className="relative">
                                        <MagnifyingGlassIcon className="absolute left-3 top-1/2 -translate-y-1/2 h-4 w-4 text-muted-foreground" />
                                        <input
                                            type="text"
                                            placeholder="Search students..."
                                            className="pl-10 pr-4 py-2 border border-border rounded-md bg-background text-foreground placeholder:text-muted-foreground focus:border-primary focus:outline-none"
                                            value={searchTerm}
                                            onChange={(e) => setSearchTerm(e.target.value)}
                                        />
                                    </div>
                                </div>
                            </div>

                            <div className="p-4 space-y-3">
                                {filteredSheets.length === 0 ? (
                                    <div className="text-center py-8 text-muted-foreground">
                                        No students found
                                    </div>
                                ) : (
                                    filteredSheets.map((sheet) => (
                                        <StudentCard
                                            key={sheet.id}
                                            sheet={sheet}
                                            onViewResults={setSelectedResult}
                                            formatResults={enhancedFormatResults}
                                        />
                                    ))
                                )}
                            </div>
                        </div>
                    </div>

                    {/* Stats Sidebar */}
                    <div className="space-y-6">
                        {classStats && (
                            <div className="bg-card dark:bg-card rounded-lg shadow-sm border border-border p-4">
                                <h3 className="font-semibold mb-3 flex items-center gap-2 text-foreground">
                                    <ArrowTrendingUpIcon className="h-5 w-5" />
                                    Class Stats
                                </h3>
                                <div className="space-y-3">
                                    <div>
                                        <p className="text-sm text-muted-foreground">Average</p>
                                        <p className="font-semibold text-foreground">{classStats.average.toFixed(1)}%</p>
                                    </div>
                                    <div>
                                        <p className="text-sm text-muted-foreground">Highest</p>
                                        <p className="font-semibold text-success-600 dark:text-success-400">{classStats.highest.toFixed(1)}%</p>
                                    </div>
                                    <div>
                                        <p className="text-sm text-muted-foreground">Lowest</p>
                                        <p className="font-semibold text-danger-600 dark:text-danger-400">{classStats.lowest.toFixed(1)}%</p>
                                    </div>
                                    <div>
                                        <p className="text-sm text-muted-foreground">Graded</p>
                                        <p className="font-semibold text-foreground">{classStats.totalStudents}/{classStats.totalSubmissions}</p>
                                    </div>
                                </div>
                            </div>
                        )}

                        <div className="bg-card dark:bg-card rounded-lg shadow-sm border border-border p-4">
                            <h3 className="font-semibold mb-3 text-foreground">Actions</h3>
                            <div className="space-y-2">
                                <button
                                    className="w-full px-4 py-2 bg-primary text-primary-foreground rounded-md hover:bg-primary/90 transition-colors disabled:opacity-50 disabled:cursor-not-allowed"
                                    disabled={!classStats}
                                >
                                    Download Results
                                </button>
                                <button
                                    className="w-full px-4 py-2 border border-border rounded-md hover:bg-accent hover:text-accent-foreground transition-colors disabled:opacity-50 disabled:cursor-not-allowed"
                                    disabled={!classStats}
                                >
                                    Share Results
                                </button>
                            </div>
                        </div>
                    </div>
                </div>
            </div>

            {/* Results Modal */}
            {selectedResult && (
                <Modal isOpen={!!selectedResult} onClose={() => setSelectedResult(null)}>
                    <div className="p-4 max-h-[95vh] overflow-hidden flex flex-col">
                        {/* Header */}
                        <div className="flex items-center justify-between mb-4 flex-shrink-0">
                            <div>
                                <h2 className="text-xl font-semibold text-foreground">Detailed Question Breakdown</h2>
                                <div className="flex items-center gap-4 mt-1">
                                    <span className="text-sm font-medium text-foreground">{selectedResult.studentName}</span>
                                    <span className="text-sm text-muted-foreground">Roll: {selectedResult.rollNumber}</span>
                                    <span className="text-sm text-muted-foreground">
                                        Score: {selectedResult.totalMarks}/{selectedResult.maxMarks} ({selectedResult.percentage}%)
                                    </span>
                                </div>
                            </div>
                            <button
                                onClick={() => setSelectedResult(null)}
                                className="text-muted-foreground hover:text-foreground transition-colors flex-shrink-0"
                            >
                                <XMarkIcon className="h-5 w-5" />
                            </button>
                        </div>

                        {/* Main Content - Side by Side Layout */}
                        <div className="flex gap-4 flex-1 min-h-0">
                            {/* PDF Viewer - Left Side */}
                            {selectedResult.pdfUrl && (
                                <div className="w-1/2 flex flex-col">
                                    <h3 className="text-sm font-medium text-foreground mb-2 flex-shrink-0">Answer Sheet</h3>
                                    <div className="flex-1 border border-border rounded-lg overflow-hidden bg-muted/30">
                                        <iframe
                                            src={selectedResult.pdfUrl ? createPdfViewerUrl(selectedResult.pdfUrl) : ''}
                                            className="w-full h-full"
                                            title={`Answer sheet for ${selectedResult.studentName}`}
                                            style={{ minHeight: '500px' }}
                                        />
                                    </div>
                                </div>
                            )}

                            {/* Question Breakdown - Right Side */}
                            <div className={`${selectedResult.pdfUrl ? 'w-1/2' : 'w-full'} flex flex-col`}>
                                <h3 className="text-sm font-medium text-foreground mb-2 flex-shrink-0">Question Analysis</h3>
                                <div className="flex-1 overflow-y-auto pr-2">
                                    <QuestionBreakdownDisplay evaluationData={selectedResult.detailedBreakdown} />
                                </div>
                            </div>
                        </div>

                        {/* Footer Actions */}
                        <div className="mt-4 flex gap-3 flex-shrink-0 border-t border-border pt-4">
                            <button
                                onClick={() => setSelectedResult(null)}
                                className="px-4 py-2 border border-border rounded-md hover:bg-accent hover:text-accent-foreground transition-colors"
                            >
                                Close
                            </button>
                            {selectedResult.pdfUrl && (
                                <button
                                    onClick={() => selectedResult.pdfUrl && window.open(createPdfViewerUrl(selectedResult.pdfUrl), '_blank')}
                                    className="px-4 py-2 bg-secondary text-secondary-foreground rounded-md hover:bg-secondary/80 transition-colors"
                                >
                                    Open PDF in New Tab
                                </button>
                            )}
                            <button className="px-4 py-2 bg-primary text-primary-foreground rounded-md hover:bg-primary/90 transition-colors">
                                Download Report
                            </button>
                        </div>
                    </div>
                </Modal>
            )}
        </div>
    );
};

export default GradingDetails;
