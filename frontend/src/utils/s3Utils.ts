const S3_CONFIG = {
    region: 'ap-south-1', // Based on backend configuration
    bucketName: import.meta.env.VITE_S3_BUCKET_NAME || 'aegisscholar-uploads', // You may need to add this to your .env
};

export const constructS3Url = (s3Key: string): string => {
    if (!s3Key) return '';
    
    // If it's already a full URL, return as is
    if (s3Key.startsWith('http://') || s3Key.startsWith('https://')) {
        return s3Key;
    }
    
    // Construct S3 URL
    return `https://${S3_CONFIG.bucketName}.s3.${S3_CONFIG.region}.amazonaws.com/${s3Key}`;
};

export const isPdfUrl = (url: string): boolean => {
    if (!url) return false;
    return url.toLowerCase().includes('.pdf') || url.toLowerCase().includes('application/pdf');
};

export const createPdfViewerUrl = (pdfUrl: string): string => {
    if (!pdfUrl) return '';
    
    const fullUrl = constructS3Url(pdfUrl);
    
    // For S3 URLs, we can embed directly
    // You might want to add additional security or use a PDF viewer service
    return fullUrl;
};

export const downloadPdfFromS3 = async (s3Key: string, filename?: string): Promise<void> => {
    try {
        const url = constructS3Url(s3Key);
        const response = await fetch(url);
        
        if (!response.ok) {
            throw new Error(`Failed to download PDF: ${response.statusText}`);
        }
        
        const blob = await response.blob();
        const downloadUrl = URL.createObjectURL(blob);
        
        const link = document.createElement('a');
        link.href = downloadUrl;
        link.download = filename || s3Key.split('/').pop() || 'document.pdf';
        document.body.appendChild(link);
        link.click();
        document.body.removeChild(link);
        
        URL.revokeObjectURL(downloadUrl);
    } catch (error) {
        console.error('Error downloading PDF:', error);
        throw error;
    }
};
