import { useCallback } from 'react';
import { useAxiosPrivate } from './useAxiosPrivate';

// Cache for presigned URLs to avoid repeated API calls
const presignedUrlCache = new Map<string, { url: string; expiry: number }>();
const CACHE_DURATION = 50 * 60 * 1000; // 50 minutes (URLs expire in 1 hour)

export const useS3Utils = () => {
    const axiosPrivate = useAxiosPrivate();

    const getPresignedUrl = useCallback(async (s3Key: string): Promise<string> => {
        if (!s3Key) return '';
        
        // If it's already a full URL, return as is
        if (s3Key.startsWith('http://') || s3Key.startsWith('https://')) {
            return s3Key;
        }
        
        // Check cache first
        const cached = presignedUrlCache.get(s3Key);
        if (cached && Date.now() < cached.expiry) {
            return cached.url;
        }
        
        try {
            // Get presigned URL from backend using authenticated axios
            const response = await axiosPrivate.post('/api/s3/presigned-url', {
                s3Key: s3Key,
                operation: 'getObject'
            });
            
            const presignedUrl = response.data.url;
            
            // Cache the URL
            presignedUrlCache.set(s3Key, {
                url: presignedUrl,
                expiry: Date.now() + CACHE_DURATION
            });
            
            return presignedUrl;
        } catch (error) {
            console.error('Error getting presigned URL:', error);
            // Fallback to direct S3 URL (less secure but functional)
            return `https://aegisscholar-uploads.s3.ap-south-1.amazonaws.com/${s3Key}`;
        }
    }, [axiosPrivate]);

    const createPdfViewerUrl = useCallback(async (pdfUrl: string): Promise<string> => {
        if (!pdfUrl) return '';
        return await getPresignedUrl(pdfUrl);
    }, [getPresignedUrl]);

    const downloadPdfFromS3 = useCallback(async (s3Key: string, filename?: string): Promise<void> => {
        try {
            const url = await getPresignedUrl(s3Key);
            const response = await fetch(url);
            
            if (!response.ok) {
                throw new Error(`Failed to download PDF: ${response.statusText}`);
            }
            
            const blob = await response.blob();
            const downloadUrl = URL.createObjectURL(blob);
            
            const link = document.createElement('a');
            link.href = downloadUrl;
            link.download = filename || s3Key.split('/').pop() || 'document.pdf';
            document.body.appendChild(link);
            link.click();
            document.body.removeChild(link);
            
            URL.revokeObjectURL(downloadUrl);
        } catch (error) {
            console.error('Error downloading PDF:', error);
            throw error;
        }
    }, [getPresignedUrl]);

    const isPdfUrl = useCallback((url: string): boolean => {
        if (!url) return false;
        return url.toLowerCase().includes('.pdf') || url.toLowerCase().includes('application/pdf');
    }, []);

    return {
        getPresignedUrl,
        createPdfViewerUrl,
        downloadPdfFromS3,
        isPdfUrl
    };
};
