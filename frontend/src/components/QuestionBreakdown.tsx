import { useMemo } from "react";

// --- Types for Question Breakdown ---
interface CriterionBreakdown {
    criterion: string;
    score: string;
    maxScore?: string;
}

interface QuestionBreakdown {
    questionNumber: string;
    marksAwarded: number;
    marksPossible: number;
    percentage: number;
    feedback: string;
    criteriaBreakdown: CriterionBreakdown[];
}

interface EvaluationBreakdown {
    totalMarks: number;
    maxMarks: number;
    overallPercentage: number;
    questions: QuestionBreakdown[];
}

// --- Question Breakdown Function ---
export const parseQuestionBreakdown = (xmlString: string): EvaluationBreakdown | null => {
    try {
        const parser = new DOMParser();
        const xmlDoc = parser.parseFromString(xmlString, 'text/xml');
        
        if (xmlDoc.getElementsByTagName("parsererror").length > 0) {
            console.error('XML parsing error');
            return null;
        }

        const evaluation = xmlDoc.querySelector('evaluation');
        if (!evaluation) return null;

        // Extract overall evaluation data
        const totalMarks = parseInt(evaluation.querySelector('total_marks')?.textContent || '0');
        const maxMarks = parseInt(evaluation.querySelector('maximum_possible_marks')?.textContent || '0');
        const overallPercentage = parseInt(evaluation.querySelector('percentage_score')?.textContent || '0');

        // Extract question details
        const questions: QuestionBreakdown[] = Array.from(evaluation.querySelectorAll('question')).map(question => {
            const questionNumber = question.getAttribute('number') || '';
            const marksAwarded = parseInt(question.querySelector('marks_awarded')?.textContent || '0');
            const marksPossible = parseInt(question.querySelector('marks_possible')?.textContent || '0');
            const percentage = marksPossible > 0 ? Math.round((marksAwarded / marksPossible) * 100) : 0;
            const feedback = question.querySelector('feedback')?.textContent?.trim() || '';

            // Extract criteria breakdown
            const marksBreakdown = question.querySelector('marks_breakdown');
            const criteriaBreakdown: CriterionBreakdown[] = [];
            
            if (marksBreakdown) {
                const criteria = Array.from(marksBreakdown.querySelectorAll('criterion'));
                
                // Group criteria with their totals
                const groupedCriteria = new Map<string, { scored: string; total: string }>();
                
                criteria.forEach(criterion => {
                    const name = criterion.getAttribute('name') || '';
                    const score = criterion.textContent || '0';
                    
                    if (name.startsWith('Total Possible for ')) {
                        const baseName = name.replace('Total Possible for ', '');
                        const existing = groupedCriteria.get(baseName) || { scored: '0', total: '0' };
                        groupedCriteria.set(baseName, { ...existing, total: score });
                    } else {
                        const existing = groupedCriteria.get(name) || { scored: '0', total: '0' };
                        groupedCriteria.set(name, { ...existing, scored: score });
                    }
                });

                // Convert to breakdown format
                groupedCriteria.forEach((value, key) => {
                    criteriaBreakdown.push({
                        criterion: key,
                        score: value.scored,
                        maxScore: value.total
                    });
                });
            }

            return {
                questionNumber,
                marksAwarded,
                marksPossible,
                percentage,
                feedback,
                criteriaBreakdown
            };
        });

        return {
            totalMarks,
            maxMarks,
            overallPercentage,
            questions
        };

    } catch (error) {
        console.error('Error parsing evaluation breakdown:', error);
        return null;
    }
};

// --- Display Component for Question Breakdown ---
const QuestionBreakdownDisplay: React.FC<{ evaluationData: EvaluationBreakdown }> = ({ evaluationData }) => {
    const getPerformanceColor = (percentage: number): string => {
        if (percentage >= 80) return "text-success-600 dark:text-success-400";
        if (percentage >= 60) return "text-primary-600 dark:text-primary-400";
        if (percentage >= 40) return "text-warning-600 dark:text-warning-400";
        return "text-danger-600 dark:text-danger-400";
    };

    const getPerformanceBg = (percentage: number): string => {
        if (percentage >= 80) return "bg-success-100 dark:bg-success-900/20";
        if (percentage >= 60) return "bg-primary-100 dark:bg-primary-900/20";
        if (percentage >= 40) return "bg-warning-100 dark:bg-warning-900/20";
        return "bg-danger-100 dark:bg-danger-900/20";
    };

    return (
        <div className="space-y-6">
            {/* Overall Performance */}
            <div className="bg-card border border-border rounded-lg p-6">
                <h2 className="text-xl font-semibold mb-4 text-foreground">Overall Performance</h2>
                <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
                    <div className="text-center">
                        <p className="text-sm text-muted-foreground">Total Score</p>
                        <p className="text-2xl font-bold text-foreground">
                            {evaluationData.totalMarks}/{evaluationData.maxMarks}
                        </p>
                    </div>
                    <div className="text-center">
                        <p className="text-sm text-muted-foreground">Percentage</p>
                        <p className={`text-2xl font-bold ${getPerformanceColor(evaluationData.overallPercentage)}`}>
                            {evaluationData.overallPercentage}%
                        </p>
                    </div>
                    <div className="text-center">
                        <p className="text-sm text-muted-foreground">Questions</p>
                        <p className="text-2xl font-bold text-foreground">{evaluationData.questions.length}</p>
                    </div>
                </div>
            </div>

            {/* Individual Question Breakdown */}
            <div className="space-y-4">
                <h2 className="text-xl font-semibold text-foreground">Question-wise Breakdown</h2>
                {evaluationData.questions.map((question, index) => (
                    <div key={question.questionNumber} className="bg-card border border-border rounded-lg overflow-hidden">
                        {/* Question Header */}
                        <div className={`p-4 ${getPerformanceBg(question.percentage)} border-b border-border`}>
                            <div className="flex justify-between items-center">
                                <h3 className="text-lg font-semibold text-foreground">
                                    {question.questionNumber}
                                </h3>
                                <div className="text-right">
                                    <p className={`text-lg font-bold ${getPerformanceColor(question.percentage)}`}>
                                        {question.marksAwarded}/{question.marksPossible}
                                    </p>
                                    <p className={`text-sm ${getPerformanceColor(question.percentage)}`}>
                                        {question.percentage}%
                                    </p>
                                </div>
                            </div>
                        </div>

                        {/* Question Content */}
                        <div className="p-4 space-y-4">
                            {/* Criteria Breakdown */}
                            {question.criteriaBreakdown.length > 0 && (
                                <div>
                                    <h4 className="font-medium mb-3 text-foreground">Marking Criteria</h4>
                                    <div className="space-y-2">
                                        {question.criteriaBreakdown.map((criterion, criterionIndex) => {
                                            const criterionPercentage = criterion.maxScore ? 
                                                Math.round((parseInt(criterion.score) / parseInt(criterion.maxScore)) * 100) : 0;
                                            
                                            return (
                                                <div key={criterionIndex} className="flex items-center justify-between p-3 bg-accent/30 dark:bg-accent/10 rounded">
                                                    <span className="text-sm text-foreground flex-1 pr-4">
                                                        {criterion.criterion}
                                                    </span>
                                                    <div className="flex items-center gap-2">
                                                        <span className="font-medium text-foreground">
                                                            {criterion.score}/{criterion.maxScore}
                                                        </span>
                                                        <span className={`text-xs px-2 py-1 rounded ${getPerformanceBg(criterionPercentage)} ${getPerformanceColor(criterionPercentage)}`}>
                                                            {criterionPercentage}%
                                                        </span>
                                                    </div>
                                                </div>
                                            );
                                        })}
                                    </div>
                                </div>
                            )}

                            {/* Feedback */}
                            {question.feedback && (
                                <div>
                                    <h4 className="font-medium mb-2 text-foreground">Detailed Feedback</h4>
                                    <div className="p-4 bg-accent/20 dark:bg-accent/10 rounded-lg">
                                        <p className="text-sm text-muted-foreground leading-relaxed">
                                            {question.feedback}
                                        </p>
                                    </div>
                                </div>
                            )}
                        </div>
                    </div>
                ))}
            </div>
        </div>
    );
};

export default QuestionBreakdownDisplay;
