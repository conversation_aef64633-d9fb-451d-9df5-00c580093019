import { useMemo } from "react";

// --- Types for Question Breakdown ---
interface CriterionBreakdown {
    criterion: string;
    score: string;
    maxScore?: string;
}

interface QuestionBreakdown {
    questionNumber: string;
    marksAwarded: number;
    marksPossible: number;
    percentage: number;
    feedback: string;
    criteriaBreakdown: CriterionBreakdown[];
}

interface EvaluationBreakdown {
    totalMarks: number;
    maxMarks: number;
    overallPercentage: number;
    questions: QuestionBreakdown[];
}

// --- Question Breakdown Function ---
export const parseQuestionBreakdown = (evaluationData: any): EvaluationBreakdown | null => {
    try {
        console.log('Parsing question breakdown, input data:', evaluationData);

        // Handle different input formats
        let xmlString = '';

        if (Array.isArray(evaluationData) && evaluationData.length > 0) {
            // Current format: Array with markdown/XML string
            xmlString = evaluationData[0];
            console.log('Using array format, extracted string:', xmlString?.substring(0, 200) + '...');
        } else if (typeof evaluationData === 'string') {
            // Direct XML string
            xmlString = evaluationData;
            console.log('Using direct string format');
        } else {
            console.error('Invalid evaluation data format:', typeof evaluationData, evaluationData);
            return null;
        }

        // Extract XML content from markdown if needed
        const xmlMatch = xmlString.match(/<evaluation>([\s\S]*?)<\/evaluation>/);
        if (!xmlMatch) {
            console.error('No evaluation XML found in data. String preview:', xmlString?.substring(0, 500));
            return null;
        }

        const xmlContent = `<evaluation>${xmlMatch[1]}</evaluation>`;
        console.log('Extracted XML content:', xmlContent.substring(0, 300) + '...');

        const parser = new DOMParser();
        const xmlDoc = parser.parseFromString(xmlContent, 'text/xml');

        if (xmlDoc.getElementsByTagName("parsererror").length > 0) {
            console.error('XML parsing error:', xmlDoc.getElementsByTagName("parsererror")[0]?.textContent);
            return null;
        }

        const evaluation = xmlDoc.querySelector('evaluation');
        if (!evaluation) {
            console.error('No evaluation element found in parsed XML');
            return null;
        }

        // Extract overall evaluation data
        const totalMarks = parseInt(evaluation.querySelector('total_marks')?.textContent || '0');
        const maxMarks = parseInt(evaluation.querySelector('maximum_possible_marks')?.textContent || '0');
        const overallPercentage = parseInt(evaluation.querySelector('percentage_score')?.textContent || '0');

        // Extract question details
        const questionElements = evaluation.querySelectorAll('question');
        console.log('Found question elements:', questionElements.length);

        const questions: QuestionBreakdown[] = Array.from(questionElements).map(question => {
            const questionNumber = question.getAttribute('number') || question.querySelector('number')?.textContent || '';
            const marksAwarded = parseInt(question.querySelector('marks_awarded')?.textContent || '0');
            const marksPossible = parseInt(question.querySelector('marks_possible')?.textContent || '0');
            const percentage = marksPossible > 0 ? Math.round((marksAwarded / marksPossible) * 100) : 0;
            const feedback = question.querySelector('feedback')?.textContent?.trim() || '';

            console.log(`Question ${questionNumber}: ${marksAwarded}/${marksPossible} (${percentage}%)`);
            console.log(`Feedback: ${feedback.substring(0, 100)}...`);

            // Extract criteria breakdown - simplified approach
            const marksBreakdown = question.querySelector('marks_breakdown');
            const criteriaBreakdown: CriterionBreakdown[] = [];

            if (marksBreakdown) {
                const criteria = Array.from(marksBreakdown.querySelectorAll('criterion'));

                criteria.forEach(criterion => {
                    const name = criterion.getAttribute('name') || '';
                    const score = criterion.textContent || '0';

                    // Skip "Total Possible" entries for now - keep it simple
                    if (!name.startsWith('Total Possible for ')) {
                        criteriaBreakdown.push({
                            criterion: name,
                            score: score,
                            maxScore: '0' // Will be updated if we find the corresponding total
                        });
                    }
                });

                // Try to match totals with criteria
                criteria.forEach(criterion => {
                    const name = criterion.getAttribute('name') || '';
                    const score = criterion.textContent || '0';

                    if (name.startsWith('Total Possible for ')) {
                        const baseName = name.replace('Total Possible for ', '');
                        const matchingCriterion = criteriaBreakdown.find(c => c.criterion === baseName);
                        if (matchingCriterion) {
                            matchingCriterion.maxScore = score;
                        }
                    }
                });
            }

            return {
                questionNumber,
                marksAwarded,
                marksPossible,
                percentage,
                feedback,
                criteriaBreakdown
            };
        });

        const result = {
            totalMarks,
            maxMarks,
            overallPercentage,
            questions
        };

        console.log('Parsed evaluation breakdown:', result);
        return result;

    } catch (error) {
        console.error('Error parsing evaluation breakdown:', error);
        return null;
    }
};

// --- Display Component for Question Breakdown ---
const QuestionBreakdownDisplay: React.FC<{ evaluationData: EvaluationBreakdown | null }> = ({ evaluationData }) => {
    // Handle null or invalid data
    if (!evaluationData) {
        return (
            <div className="bg-card border border-border rounded-lg p-6 text-center">
                <p className="text-muted-foreground">Unable to load question breakdown data.</p>
            </div>
        );
    }
    const getPerformanceColor = (percentage: number): string => {
        if (percentage >= 80) return "text-green-600 dark:text-green-400";
        if (percentage >= 60) return "text-blue-600 dark:text-blue-400";
        if (percentage >= 40) return "text-yellow-600 dark:text-yellow-400";
        return "text-red-600 dark:text-red-400";
    };

    const getPerformanceBg = (percentage: number): string => {
        if (percentage >= 80) return "bg-green-50 dark:bg-green-900/20";
        if (percentage >= 60) return "bg-blue-50 dark:bg-blue-900/20";
        if (percentage >= 40) return "bg-yellow-50 dark:bg-yellow-900/20";
        return "bg-red-50 dark:bg-red-900/20";
    };

    return (
        <div className="space-y-6">
            {/* Overall Performance */}
            <div className="bg-card border border-border rounded-lg p-6">
                <h2 className="text-xl font-semibold mb-4 text-foreground">Overall Performance</h2>
                <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
                    <div className="text-center">
                        <p className="text-sm text-muted-foreground">Total Score</p>
                        <p className="text-2xl font-bold text-foreground">
                            {evaluationData.totalMarks}/{evaluationData.maxMarks}
                        </p>
                    </div>
                    <div className="text-center">
                        <p className="text-sm text-muted-foreground">Percentage</p>
                        <p className={`text-2xl font-bold ${getPerformanceColor(evaluationData.overallPercentage)}`}>
                            {evaluationData.overallPercentage}%
                        </p>
                    </div>
                    <div className="text-center">
                        <p className="text-sm text-muted-foreground">Questions</p>
                        <p className="text-2xl font-bold text-foreground">{evaluationData.questions.length}</p>
                    </div>
                </div>
            </div>

            {/* Individual Question Breakdown */}
            <div className="space-y-4">
                <h2 className="text-xl font-semibold text-foreground">Question-wise Breakdown</h2>
                {evaluationData.questions.map((question) => (
                    <div key={question.questionNumber} className="bg-card border border-border rounded-lg overflow-hidden">
                        {/* Question Header */}
                        <div className={`p-4 ${getPerformanceBg(question.percentage)} border-b border-border`}>
                            <div className="flex justify-between items-center">
                                <h3 className="text-lg font-semibold text-foreground">
                                    {question.questionNumber}
                                </h3>
                                <div className="text-right">
                                    <p className={`text-lg font-bold ${getPerformanceColor(question.percentage)}`}>
                                        {question.marksAwarded}/{question.marksPossible}
                                    </p>
                                    <p className={`text-sm ${getPerformanceColor(question.percentage)}`}>
                                        {question.percentage}%
                                    </p>
                                </div>
                            </div>
                        </div>

                        {/* Question Content */}
                        <div className="p-4 space-y-4">
                            {/* Criteria Breakdown */}
                            {question.criteriaBreakdown.length > 0 && (
                                <div>
                                    <h4 className="font-medium mb-3 text-foreground">Marking Criteria</h4>
                                    <div className="space-y-2">
                                        {question.criteriaBreakdown.map((criterion, criterionIndex) => {
                                            const criterionPercentage = criterion.maxScore ? 
                                                Math.round((parseInt(criterion.score) / parseInt(criterion.maxScore)) * 100) : 0;
                                            
                                            return (
                                                <div key={criterionIndex} className="flex items-center justify-between p-3 bg-accent/30 dark:bg-accent/10 rounded">
                                                    <span className="text-sm text-foreground flex-1 pr-4">
                                                        {criterion.criterion}
                                                    </span>
                                                    <div className="flex items-center gap-2">
                                                        <span className="font-medium text-foreground">
                                                            {criterion.score}/{criterion.maxScore}
                                                        </span>
                                                        <span className={`text-xs px-2 py-1 rounded ${getPerformanceBg(criterionPercentage)} ${getPerformanceColor(criterionPercentage)}`}>
                                                            {criterionPercentage}%
                                                        </span>
                                                    </div>
                                                </div>
                                            );
                                        })}
                                    </div>
                                </div>
                            )}

                            {/* Feedback */}
                            {question.feedback && (
                                <div>
                                    <h4 className="font-medium mb-2 text-foreground">Detailed Feedback</h4>
                                    <div className="p-4 bg-accent/20 dark:bg-accent/10 rounded-lg">
                                        <p className="text-sm text-muted-foreground leading-relaxed">
                                            {question.feedback}
                                        </p>
                                    </div>
                                </div>
                            )}
                        </div>
                    </div>
                ))}
            </div>
        </div>
    );
};

export default QuestionBreakdownDisplay;
