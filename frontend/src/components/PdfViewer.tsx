import React, { useState, useEffect } from 'react';
import { useS3Utils } from '@/hooks/useS3Utils';

interface PdfViewerProps {
    s3Key: string;
    title: string;
    className?: string;
    style?: React.CSSProperties;
}

const PdfViewer: React.FC<PdfViewerProps> = ({ s3Key, title, className, style }) => {
    const [pdfUrl, setPdfUrl] = useState<string>('');
    const [loading, setLoading] = useState<boolean>(true);
    const [error, setError] = useState<string>('');
    const { createPdfViewerUrl } = useS3Utils();

    useEffect(() => {
        const loadPdfUrl = async () => {
            if (!s3Key) {
                setLoading(false);
                return;
            }

            try {
                setLoading(true);
                setError('');
                const url = await createPdfViewerUrl(s3Key);
                setPdfUrl(url);
            } catch (err) {
                console.error('Error loading PDF URL:', err);
                setError('Failed to load PDF');
            } finally {
                setLoading(false);
            }
        };

        loadPdfUrl();
    }, [s3Key, createPdfViewerUrl]);

    if (loading) {
        return (
            <div className={`flex items-center justify-center bg-muted/30 ${className}`} style={style}>
                <div className="text-center">
                    <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-primary mx-auto mb-2"></div>
                    <p className="text-sm text-muted-foreground">Loading PDF...</p>
                </div>
            </div>
        );
    }

    if (error) {
        return (
            <div className={`flex items-center justify-center bg-muted/30 ${className}`} style={style}>
                <div className="text-center">
                    <p className="text-sm text-destructive mb-2">{error}</p>
                    <button 
                        onClick={() => window.location.reload()}
                        className="text-xs text-primary hover:underline"
                    >
                        Retry
                    </button>
                </div>
            </div>
        );
    }

    if (!pdfUrl) {
        return (
            <div className={`flex items-center justify-center bg-muted/30 ${className}`} style={style}>
                <p className="text-sm text-muted-foreground">No PDF available</p>
            </div>
        );
    }

    return (
        <iframe
            src={pdfUrl}
            className={className}
            title={title}
            style={style}
        />
    );
};

export default PdfViewer;
